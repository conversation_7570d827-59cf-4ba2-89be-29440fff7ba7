/* 时间线样式 - 基于图片设计 */
.timeline-container {
  position: relative;
  padding-left: 3rem;
  margin-top: 2rem;
}

/* 垂直连接线 */
.timeline-container::before {
  content: '';
  position: absolute;
  left: 1.5rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e0e0e0;
  z-index: 1;
}

/* 时间线项目 */
.timeline-item {
  position: relative;
  margin-bottom: 2.5rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.timeline-item:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 圆形标记点 */
.timeline-item::before {
  content: '';
  position: absolute;
  left: -2.25rem;
  top: 1.5rem;
  width: 12px;
  height: 12px;
  background-color: #ffffff;
  border: 3px solid #4a90e2;
  border-radius: 50%;
  z-index: 2;
  box-shadow: 0 0 0 4px #ffffff;
}

/* 连接箭头 */
.timeline-item::after {
  content: '';
  position: absolute;
  left: -1.5rem;
  top: 1.5rem;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 12px solid #ffffff;
  z-index: 1;
}

/* 时间线日期头部 */
.timeline-date {
  background-color: #f8f9fa;
  padding: 1.25rem 1.5rem 1rem;
  color: #6c757d;
  font-size: 0.875rem;
  font-weight: 600;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.timeline-date span {
  color: #495057;
  font-weight: 700;
  font-size: 1rem;
}

/* 时间线内容区域 */
.timeline-content {
  padding: 1.5rem;
  background-color: #ffffff;
}

.timeline-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 详情行 */
.detail-row {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f8f9fa;
  gap: 0.5rem;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.875rem;
  line-height: 1.4;
  width: 5rem;
}

.detail-value {
  color: #212529;
  font-size: 0.875rem;
  line-height: 1.4;
  text-align: left;
  word-break: break-word;
  padding-left: 1rem;
}

/* 深色主题样式 */
body:not(.light-theme) .timeline-container::before {
  background-color: rgba(255, 255, 255, 0.2);
}

body:not(.light-theme) .timeline-item {
  background-color: var(--container-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

body:not(.light-theme) .timeline-item::before {
  background-color: var(--body-color);
  border-color: var(--first-color);
  box-shadow: 0 0 0 4px var(--body-color);
}

body:not(.light-theme) .timeline-item::after {
  border-right-color: var(--container-color);
}

body:not(.light-theme) .timeline-date {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-color-light);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

body:not(.light-theme) .timeline-date span {
  color: var(--title-color);
}

body:not(.light-theme) .detail-label {
  color: var(--text-color-light);
}

body:not(.light-theme) .detail-value {
  color: var(--text-color);
}

body:not(.light-theme) .detail-row {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .timeline-container {
    padding-left: 2rem;
  }

  .timeline-container::before {
    left: 1rem;
  }

  .timeline-item::before {
    left: -1.75rem;
  }

  .timeline-item::after {
    left: -1rem;
  }

  .timeline-date {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .detail-row {
    /* flex-direction: column; */
    gap: 0.25rem;
    align-items: flex-start;
  }

  .detail-label {
    min-width: auto;
    margin-bottom: 0.25rem;
  }

  .detail-value {
    text-align: left;
    max-width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .timeline-container {
    padding-left: 1.5rem;
  }

  .timeline-container::before {
    left: 0.75rem;
  }

  .timeline-item::before {
    left: -1.5rem;
  }

  .timeline-item::after {
    left: -0.75rem;
  }

  .timeline-date {
    padding: 1rem;
  }

  .timeline-content {
    padding: 1rem;
  }
} 